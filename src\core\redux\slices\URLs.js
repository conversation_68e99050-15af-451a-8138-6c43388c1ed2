const env = import.meta.env.VITE_REACT_APP_ENV;

let MainDomain = '';

switch (env) {
    case 'sandbox':
        MainDomain = import.meta.env.VITE_SANDBOX_API_URL;
        break;
    case 'dev':
        MainDomain = import.meta.env.VITE_DEV_API_URL;
        break;
    case 'sim':
        MainDomain = import.meta.env.VITE_SIM_API_URL;
        break;
    case 'prod':
    default:
        MainDomain = import.meta.env.VITE_PROD_API_URL;
        break;
}

export { MainDomain };

//app.codpowergroup.dev
//api-seller.codpowergroup.com
// app_sellers
// cu!95m4G8

// api_sellers
// t886$vX4g

export const register = MainDomain + "api/auth/register"
export const login = MainDomain + "login"
export const autologin = MainDomain + "autoLogin/"
export const forgotpassword = MainDomain + "api/auth/forgotpassword"
export const resetpassword = MainDomain + "api/auth/resetpassword"

export const getUsers = MainDomain + "api/auth/admins/"
export const getRoles = MainDomain + "api/auth/roles/"
export const toggleBanning = MainDomain + "api/auth/delete-user/" //+ id



export const settings = MainDomain + "api/settings"

//Lead sources
export const lightFunnelsStateSavingUrl = MainDomain + "lightf/auth/save_state"
export const lightFunnelsInstallUrl = MainDomain + "lightf/auth/install"
export const youcanInstallUrl = MainDomain + "youcan/auth/install"
export const dropifyCheckUrl = MainDomain + "dropify/auth/authorize"
export const installDropifyUrl = MainDomain + "dropify/auth/install"
export const uploadExcelFileUrl = MainDomain + "orders/excel/import"
export const shopifyInstallUrl = MainDomain + "shopify/auth/install"
export const addShopifyLeadSourceUrl = MainDomain + "shopify/addSourceLead"
export const getLeadSourcesUrl = MainDomain + "leadsources/list"
export const deleteLeadSourcesUrl = MainDomain + "leadsources/delete"
export const installGoogleSheetUrl = MainDomain + "google-sheets/setup"
export const importOrdersGoogleSheetUrl = MainDomain + "google-sheets/install"
export const forceimportOrdersGoogleSheetUrl = MainDomain + "google-sheets/import/"
export const DeleteGoogleSheetAccountUrl = MainDomain + "google-sheets/delete"

//orders management
export const ordersListUrl = MainDomain + "orders/list"
export const ordersExportUrl = MainDomain + "orders/export"
export const orderStatusUrl = MainDomain + "orders/statuses"
export const followupStatusUrl = MainDomain + "orders/followup/statuses"
export const orderPayementsUrl = MainDomain + "orders/payments"


//invoices
export const serviceInvoicesListUrl = MainDomain + "invoices/list"
export const sourcingInvoicesListUrl = MainDomain + "sourcing/invoices/list"
export const sourcingInvoicesDetailsUrl = MainDomain + "sourcing/invoices/details/"
export const invoicesStatusUrl = MainDomain + "invoices/statuses"

//sourcing
export const addSourcingRequestUrl = MainDomain + "sourcing/store";
export const updateSourcingRequestUrl = MainDomain + "sourcing/update/";
export const deleteSourcingRequestUrl = MainDomain + "sourcing/delete/";
export const getSourcingRequestUrl = MainDomain + "sourcing/list";
export const getSourcingRequesDetailstUrl = MainDomain + "sourcing/details/";

//dashbaord
export const dashboardFundsDataUrl = MainDomain + "kpis/funds"
export const dashboardShippingDataUrl = MainDomain + "kpis/shipping"
export const dashboardCallCenterDataUrl = MainDomain + "kpis/confirmation"
export const dashboardFollowupDataUrl = MainDomain + "kpis/followup"

//statistics
export const productStatisticsUrl = MainDomain + "kpis/orders"
export const productFundsUrl = MainDomain + "kpis/funds"

//Products
export const productsListUrl = MainDomain + "products/list"
export const getProductUrl = MainDomain + "products/";
export const getProductInventoryUrl = MainDomain + "products/inventory/";
export const getAttributesUrl = MainDomain + "attributes/list";
export const AddAttributeUrl = MainDomain + "attributes/create";
export const updateAttributeUrl = MainDomain + "attributes/update/"; // + attributeId
export const deleteAttributeUrl = MainDomain + "attributes/delete/"; // + attributeId
export const getAttributeDetailsUrl = MainDomain + "attributes/details/"; // + attributeId
export const deleteProductUrl = MainDomain + "products/archive/";
export const unarchiveProductUrl = MainDomain + "products/unarchive/";
export const getProductCategoriesUrl = MainDomain + "products/categories";
export const getProductTypeUrl = MainDomain + "products/types";
export const getUpdateProductUrl = MainDomain + "products/update/";
export const createProductUrl = MainDomain + "products/create";

//tools
export const simulatorUrl = MainDomain + "tools/simulator"


// Sales Price 
export const createSalesPriceUrl = MainDomain + "products/offer/create/"; // + productId
export const updateSalesPriceUrl = MainDomain + "products/offer/update/"; // + productId + "/" + salesPriceId
export const deleteSalesPriceUrl = MainDomain + "products/offer/delete/"; // + productId + "/" + salesPriceId

// Sales Price 
export const createVariantsUrl = MainDomain + "products/variant/create/"; // + productId
export const updateVariantseUrl = MainDomain + "products/variant/update/"; // + productId + "/" + salesPriceId
export const deleteVariantsUrl = MainDomain + "products/variant/delete/"; // + productId + "/" + salesPriceId

// Upsell endpoints – fixed parts only
export const createUpsellUrl = MainDomain + "products/upsell/create/";   // + productId
export const updateUpsellUrl = MainDomain + "products/upsell/update/";   // + productId + "/" + upsellId
export const deleteUpsellUrl = MainDomain + "products/upsell/delete/";   // + productId + "/" + upsellId


//generals
export const countriesUrl = MainDomain + "countries";
export const productListUrl = MainDomain + "products/list";
export const currenciestUrl = MainDomain + "currencies";
export const modelsUrl = MainDomain + "models";
export const shippingMethodsUrl = MainDomain + "shippingMethods";
export const processModeUrl = MainDomain + "processMode";

//profile
export const updateProfileUrl = MainDomain + "profile";
export const getFeesUrl = MainDomain + "profile/fees";
export const updatePasswordUrl = MainDomain + "profile/password";
export const forgotPasswordUrl = MainDomain + "forgotpassword";
export const forgotPasswordOTPCheckUrl = MainDomain + "resetCodeCheck";
export const resetPasswordUrl = MainDomain + "resetPassword";
export const twofaStatusUrl = MainDomain + "2fa/status";
export const toggle2faUrl = MainDomain + "2fa/toggle"; 