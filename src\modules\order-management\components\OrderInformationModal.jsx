import { Button } from "@heroui/button";
import CustomModal from "@shared/components/CustomModal.jsx";
import { Tab, Tabs } from "@heroui/tabs";
import { Airplane01Icon, CallIcon, Clock02Icon, ContainerTruckIcon, DocumentValidationIcon, InformationCircleIcon, PackageDeliveredIcon, PackageIcon, ReturnRequestIcon, ShippingTruck01Icon, TaskAdd01Icon, UserIcon } from "hugeicons-react";
import DataTile from "@shared/components/DataTile.jsx";
import { MainDomain } from "../../../core/redux/slices/URLs";
import axios from "axios";
import { getToken } from "../../../core/services/TokenHandler";
import React, { useCallback, useEffect, useState } from "react";
import { Chip, Spinner, Tooltip } from "@heroui/react";
import { section } from "framer-motion/client";
import moment from "moment";
import { motion } from "framer-motion";
import { statusColorMap, statusIconsMap } from "../../../core/utils/functions";
import { usePermissions } from "../../../core/providers/PermissionContext";
import PriceRenderer from "../../shared/components/PriceRenderer";

const TimeLine = ({ steps, currentStep }) => {
    const stepKeys = Object.keys(steps);
    const currentIndex = stepKeys.indexOf(currentStep);
    const array = stepKeys; // use all steps, not slice

    const icons = [
        TaskAdd01Icon,
        ContainerTruckIcon,
        Airplane01Icon,
        PackageDeliveredIcon,
        ReturnRequestIcon,
    ];

    return (
        <div className="flex items-center font-sans select-none">
            {array.map((stepKey, i) => (
                <React.Fragment key={stepKey}>
                    {/* Step label with icon */}
                    <div className="flex flex-col p-2 justify-center rounded-full items-center">
                        {React.createElement(icons[i], {
                            className:
                                (i <= currentIndex ? 'bg-glb_blue' : 'bg-gray-500') +
                                ' text-white rounded-full p-2',
                            size: 40,
                        })}
                        <h6
                            className={`${i <= currentIndex ? 'text-glb_blue' : 'opacity-60'
                                } whitespace-nowrap font-semibold text-[12px]`}
                        >
                            {steps[stepKey]}
                        </h6>
                    </div>

                    {/* Line between steps */}
                    {i < array.length - 1 && (
                        <div className={`relative flex-1 h-0.5 mx-2 overflow-hidden border-b-2 border-dashed border-glb_blue dark:border-glb_blue ${i < currentIndex ? '' : 'opacity-20'}`}>
                            {i < currentIndex && (
                                <motion.div
                                    className="absolute flex gap-1 top-0 left-0 h-0.5"
                                    initial={{ x: '-100%' }}
                                    animate={{ x: '100%' }}
                                    transition={{
                                        duration: 4.5,
                                        repeat: Infinity,
                                        ease: 'linear',
                                    }}
                                    style={{ width: '200%' }}
                                >
                                    {/* Repeated small dash blocks */}
                                    {Array(40)
                                        .fill(0)
                                        .map((_, idx) => (
                                            <div
                                                key={idx}
                                                className="w-2 h-0.5 bg-glb_blue rounded-sm"
                                            />
                                        ))}
                                </motion.div>
                            )}
                        </div>
                    )}
                </React.Fragment>
            ))}
        </div>
    );
};


const OrderInformationModal = ({ isOpen, onClose }) => {

    const { permissions, hasPermission } = usePermissions();
    const [selectedTab, setSelectedTab] = useState('information')
    const [order, setOrder] = useState(null)
    const handleOnClose = () => {
        onClose()
        setSelectedTab('information')
        setOrder(null);
        setSections([
            { key: 'information', label: 'Information', icon: InformationCircleIcon, tabs: null },
            { key: 'customer', label: 'Customer', icon: UserIcon, tabs: null },
            { key: 'products', label: 'Products', icon: PackageIcon, tabs: null },
            { key: 'calls', label: 'Confirmation Calls', icon: CallIcon, tabs: null, permission: 'sellers.calls' },
            { key: 'confirmation', label: 'Confirmation History', icon: DocumentValidationIcon, tabs: null, permission: 'sellers.confirmation' },
            { key: 'shipping', label: 'Shipping History', icon: ShippingTruck01Icon, tabs: null, permission: 'sellers.shipping' },
            { key: 'followup', label: 'Follow Up', icon: Clock02Icon, tabs: null, permission: 'sellers.followup' },
        ]);
        setSectionCounts(null);
        setLoadingTbasData(false);
    }
    // Define row class names for tables
    const tableRowClassNames = {
        even: 'bg-white dark:bg-[#ffffff10] h-12',
        odd: 'bg-[#00000010] dark:bg-[#ffffff05] h-12'
    }
    const TableComponent = ({
        loading,
        columns,
        data,
        renderCell,
        rowClassNames = {
            even: 'bg-white dark:bg-[#ffffff10] h-12',
            odd: 'bg-[#00000010] dark:bg-[#ffffff05] h-12'
        },
        emptyMessage = "No Data Available"
    }) => {
        // Default renderCell function if none is provided
        const defaultRenderCell = (item, columnKey) => {
            if (columnKey.toLowerCase().includes('date') && item[columnKey]) {
                return moment(item[columnKey]).format('DD-MM-YYYY HH:mm');
            } else if (typeof item[columnKey] === 'string') {
                return item[columnKey].charAt(0).toUpperCase() + item[columnKey].slice(1);
            } else {
                return item[columnKey];
            }
        };

        // Use the provided renderCell function or fall back to the default
        const cellRenderer = renderCell || defaultRenderCell;

        return (
            <div>

                <div className="overflow-x-auto flex-grow">
                    {/* <div className="w-[90%] mx-auto p-4 min-w-[600px] ">
                        <TimeLine steps={{ creation: 'Created', pickup: 'Picked', outForDelivery: 'Out for delivery', delivred: 'Delivred', returned: 'Returned' }} currentStep={'pickup'} />
                    </div> */}
                    <table className="box-border border-collapse overflow-auto min-w-full">
                        <thead>
                            <tr className="border-b border-gray-300 h-11 dark:border-gray-600">
                                {columns.map((col) => (
                                    <th key={col.key} className="dark:bg-[#ffffff05] mx-6 whitespace-nowrap text-center px-10 py-2 text-[#00000060] dark:text-[#ffffff60] text-base font-medium">
                                        {col.label}
                                    </th>
                                ))}
                            </tr>
                        </thead>
                        <tbody>
                            {loading ? (
                                // Loading skeleton
                                (Array.from({ length: 5 }).map((_, rowIndex) => (
                                    <motion.tr
                                        key={rowIndex}
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.3, delay: rowIndex * 0.1 }}
                                        className={`h-14 ${rowIndex % 2 === 0 ? rowClassNames.even : rowClassNames.odd}`}
                                    >
                                        {columns.map((_, colIndex) => (
                                            <td
                                                key={colIndex}
                                                className="px-1 py-2 text-center dark:text-gray-300 text-sm whitespace-nowrap"
                                            >
                                                <motion.div
                                                    initial={{ opacity: 0 }}
                                                    animate={{ opacity: [0, 1, 0] }}
                                                    transition={{
                                                        duration: 1,
                                                        repeat: Infinity,
                                                        repeatType: "loop",
                                                        repeatDelay: 0.5,
                                                        delay: colIndex * 0.1,
                                                    }}
                                                    className="h-4 bg-gray-300 dark:bg-gray-700 rounded"
                                                />
                                            </td>
                                        ))}
                                    </motion.tr>
                                )))
                            ) : data && data.length > 0 ? (
                                // Actual data rows
                                (data.map((row, index) => {
                                    const rowClass = index % 2 === 0 ? rowClassNames.even : rowClassNames.odd;
                                    return (
                                        <tr key={index}>
                                            {columns.map((col) => (
                                                <td key={col.key} className={`${rowClass} px-1 py-2 text-center dark:text-gray-300 text-sm whitespace-nowrap`}>
                                                    {cellRenderer(row, col.key)}
                                                </td>
                                            ))}
                                        </tr>
                                    );
                                }))
                            ) : (
                                // Empty state
                                (<tr>
                                    <td colSpan={columns.length} className="text-center p-4">
                                        {emptyMessage}
                                    </td>
                                </tr>)
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        );
    };

    const [Sections, setSections] = useState([
        { key: 'information', label: 'Information', icon: InformationCircleIcon, tabs: null },
        { key: 'customer', label: 'Customer', icon: UserIcon, tabs: null },
        { key: 'products', label: 'Products', icon: PackageIcon, tabs: null },
        { key: 'calls', label: 'Confirmation Calls', icon: CallIcon, tabs: null, permission: 'sellers.calls' },
        { key: 'confirmation', label: 'Confirmation History', icon: DocumentValidationIcon, tabs: null, permission: 'sellers.confirmation' },
        { key: 'shipping', label: 'Shipping History', icon: ShippingTruck01Icon, tabs: null, permission: 'sellers.shipping' },
        { key: 'followup', label: 'Follow Up', icon: Clock02Icon, tabs: null, permission: 'sellers.followup' }, // Updated icon
    ])

    const [loadingTbasData, setLoadingTbasData] = useState(false)
    const [sectionCounts, setSectionCounts] = useState(null)

    // Custom renderCell function for tables
    const renderTableCell = (item, columnKey) => {
        switch (columnKey) {
            case 'date':
                return item[columnKey] ? moment(item[columnKey]).format('DD-MM-YYYY HH:mm') : '-';
            case 'feedback':
                if (item['status']?.includes('-')) {
                    return item['status'].split('-')[0]
                }
                else {
                    return item['status'];
                }
            case 'scheduleDate':
                return item['status']?.includes('-') ? moment(item['status'].split('-')[1], "DD/MM/YYYY HH:mm").format('DD-MM-YYYY HH:mm')
                    : '-';
            case 'status':
                // Apply styling to status cells
                const statusColor = statusColorMap[item[columnKey]] || 'gray';
                const StatusIcon = statusIconsMap[item[columnKey]];
                return (
                    <div className="flex items-center justify-center gap-1">
                        {StatusIcon && <StatusIcon size={16} className={`text-${statusColor}-500`} />}
                        <span className={`text-${statusColor}-500 font-medium`}>
                            {typeof item[columnKey] === 'string'
                                ? item[columnKey].charAt(0).toUpperCase() + item[columnKey].slice(1)
                                : item[columnKey]}
                        </span>
                    </div>
                );


            case 'answer':
                // Apply styling to feedback/answer cells
                const feedbackColor =
                    item[columnKey]?.toLowerCase() === 'yes' ? 'text-green-500' :
                        item[columnKey]?.toLowerCase() === 'no' ? 'text-red-500' : '';
                return (
                    <span className={`${feedbackColor} font-medium`}>
                        {typeof item[columnKey] === 'string'
                            ? item[columnKey].charAt(0).toUpperCase() + item[columnKey].slice(1)
                            : item[columnKey] || '-'}
                    </span>
                );
            case 'price':
                // Format price with currency if available
                return (<PriceRenderer additional={item[columnKey]} /> || '-');
            case 'comment':
                // Truncate long comments and add tooltip
                if (!item[columnKey]) return '-';
                const comment = item[columnKey].toString();
                const truncated = comment.length > 50 ? `${comment.substring(0, 50)}...` : comment;
                return (
                    <Tooltip content={comment} placement="bottom">
                        <span className="cursor-help">{truncated}</span>
                    </Tooltip>
                );
            default:
                // Default formatting for all other cell types
                if (typeof item[columnKey] === 'string') {
                    return item[columnKey].charAt(0).toUpperCase() + item[columnKey].slice(1);
                }
                return item[columnKey] || '-';
        }
    };


    const renderInfoCell = useCallback((label, value) => {

        switch (label) {
            case "Total Price":
                return (

                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        <div >
                            {label}
                        </div>
                        {loadingTbasData ? <Spinner size="sm" /> : <div >
                            <Chip className="capitalize text-black dark:text-white bg-glb_green/25 border-1 border-glb_green " variant="flat">
                                <PriceRenderer additional={value} />
                            </Chip>
                        </div>}
                    </div>
                );
            case "Status":
                return (
                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        <div >
                            {label}
                        </div>
                        {loadingTbasData ? <Spinner size="sm" /> : <div className="text-sm font-normal text-end" dangerouslySetInnerHTML={{ __html: value }} />}
                    </div>
                );
            case 'Created At':
                return (
                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        <div >
                            {label}
                        </div>
                        {loadingTbasData ? <Spinner size="sm" /> : <div className="text-sm" >{value ? moment(value).format('DD/MM/YYYY HH:mm') : '-'}
                        </div>}
                    </div>
                );
            case 'Shipped At':
                return (
                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        <div >
                            {label}
                        </div>
                        {loadingTbasData ? <Spinner size="sm" /> : <div className="text-sm" >{value ? moment(value).format('DD/MM/YYYY HH:mm') : '-'}
                        </div>}
                    </div>
                );
            case 'Product':
                return (
                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-start font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        <div >
                            {label}
                        </div>

                        <div
                            className=" truncate cursor-help max-w-[250px] overflow-hidden text-ellipsis"
                        >
                            <div dangerouslySetInnerHTML={{ __html: value }}
                            />
                        </div>

                    </div>
                );
            case 'Delivered At':
                return (
                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        <div >
                            {label}
                        </div>
                        {loadingTbasData ? <Spinner size="sm" /> : <div className="text-sm" >
                            {value ? moment(value).format('DD/MM/YYYY c') : '-'}

                        </div>}
                    </div>
                );
            case 'Returned At':
                return (
                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        {loadingTbasData ? <Spinner /> : <div >
                            {label}
                        </div>}
                        {loadingTbasData ? <Spinner size="sm" /> : <div className="text-sm" >
                            {value ? moment(value).format('DD/MM/YYYY HH:mm') : '-'}

                        </div>}
                    </div>
                );
            case 'Payment Method':
                return (
                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        <div >
                            {label}
                        </div>
                        {loadingTbasData ? <Spinner size="sm" /> : <div className="text-sm" >
                            <div className={`${value === 'cod' ? 'text-warning' : 'text-glb_green'} font-bold capitalize  `} >
                                {value === 'cod' ? 'COD' : 'PREPAID'}
                            </div>
                        </div>}
                    </div>
                );
            case 'Is Upsell':
                return (
                    <div
                        className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                        <div >
                            {label}
                        </div>
                        {loadingTbasData ? <Spinner size="sm" /> : <div >
                            <div className={`${value === 'no' ? 'text-glb_red' : 'text-glb_green'}  p-1 rounded font-bold capitalize  `} >
                                {value === 'no' ? 'No' : 'Yes'}
                            </div>
                        </div>}
                    </div>
                );
            default:
                return (<div
                    className=" py-4 px-2 lg:px-16 flex justify-between items-center font-normal text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10">
                    <div >
                        {label}
                    </div>
                    {value ? <div className="text-sm" dangerouslySetInnerHTML={{ __html: value }} /> : '-'
                    }
                </div>)
        }
    }, []);



    useEffect(() => {
        if (!isOpen) return;
        const loadInfoData = async () => {
            const existingCustomerData = Sections.find(section => section.key === 'information')?.tabs;
            if (existingCustomerData) return;
            setLoadingTbasData(true)
            try {
                let endpoint = `${MainDomain}orders/${isOpen?.id}`;

                const response = await axios.get(endpoint,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    });

                if (response.data.response !== "success") {
                    throw new Error("Failed to fetch data");
                }
                setSectionCounts({
                    calls: response.data.countCalls,
                    confirmation: response.data.countConfirmations,
                    products: response.data.countProducts,
                    shipping: response.data.countShipping,
                    followup: response.data.countFollowup,
                })
                setOrder(response.data.result)
                const informationTabs = {
                    "Order N°": response.data.result.orderNum,
                    ...(hasPermission('sellers.trackingnumber') && {
                        "Tracking N°": response.data.result.trackingNumber
                    }),
                    "Store": response.data.result.store,
                    "Product": response.data.result.goodsDescription,
                    "Total Price": response.data.result.goodsValue + " " + response.data.result.currency,
                    "Status": response.data.result.statusDescription,
                    "Applied offer": response.data.result.appliedOffer,
                    "Payment Method": response.data.result.paymentMethod,
                    "Is Upsell": response.data.result.isUpsell,
                    "Note": response.data.result.description,
                    "Created At": response.data.result.followup.createdAt,
                    "Shipped At": response.data.result.shipping.shippedAt,

                    ...(response.data.result.shipping.deliveredAt && {
                        "Delivered At": response.data.result.shipping.deliveredAt
                    }),
                    ...(response.data.result.shipping.returnedAt && {
                        "Returned At": response.data.result.shipping.returnedAt
                    }),
                    ...(response.data.result.shipping.callcenterInvoice && {
                        "Call Center Invoice": response.data.result.shipping.callcenterInvoice
                    }),
                    ...(response.data.result.shipping.shippingInvoice && {
                        "Shipping Invoice": response.data.result.shipping.shippingInvoice
                    }),
                    ...(response.data.result.shipping.remittanceInvoice && {
                        "Remittance Invoice": response.data.result.shipping.remittanceInvoice
                    }),
                }

                setSections(prevSections =>
                    prevSections.map(section =>
                        section.key === 'information'
                            ? { ...section, tabs: informationTabs }
                            : section
                    )
                );

            } catch (error) {
                console.error(`Error fetching order ${isOpen?.id} data:`, error);
                return {};
            } finally {
                setLoadingTbasData(false)
            }

        }
        const loadCustomerData = async () => {

            const existingCustomerData = Sections.find(section => section.key === 'customer')?.tabs;
            if (existingCustomerData) return;
            setLoadingTbasData(true)
            try {
                let endpoint = `${MainDomain}orders/${isOpen?.id}/customer`;

                const response = await axios.get(endpoint,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    });

                if (response.data.response !== "success") {
                    throw new Error("Failed to fetch data");
                }

                const cutomerTabs = {
                    "Full Name": response.data.result.contact,
                    "Mobile Number": response.data.result.mobileNumber,
                    "Whatsapp Number": response.data.result.whatsappNumber,
                    "Second Phone Number": response.data.result.phoneNumber,
                    "Country": response.data.result.country,
                    "City": response.data.result.city,
                    "Area": response.data.result.area,
                    "Street": response.data.result.street,
                    "House Number": response.data.result.houseNumber,
                    "Nearest Place": response.data.result.nearestPlace,
                    "Address": response.data.result.address,
                    "GPS coordinates": response.data.result.gps

                }

                setSections(prevSections =>
                    prevSections.map(section =>
                        section.key === 'customer'
                            ? { ...section, tabs: cutomerTabs }
                            : section
                    )
                );

            } catch (error) {
                console.error(`Error fetching order ${isOpen?.id} data:`, error);
                return {};
            } finally {
                setLoadingTbasData(false)
            }
        }
        const loadProductsData = async () => {
            const existingCustomerData = Sections.find(section => section.key === 'products')?.tabs;
            if (existingCustomerData) return;
            setLoadingTbasData(true)
            try {
                let endpoint = `${MainDomain}orders/${isOpen?.id}/products`;

                const response = await axios.get(endpoint,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    });

                if (response.data.response !== "success") {
                    throw new Error("Failed to fetch data");
                }

                setSectionCounts(prevCounts => ({
                    ...prevCounts,
                    products: response.data.count,
                }));
                setSections(prevSections =>
                    prevSections.map(section =>
                        section.key === 'products'
                            ? { ...section, tabs: response.data.result }
                            : section
                    )
                );

            } catch (error) {
                console.error(`Error fetching order ${isOpen?.id} data:`, error);
                return {};
            } finally {
                setLoadingTbasData(false)
            }
        }
        const loadCallsData = async () => {
            const existingCustomerData = Sections.find(section => section.key === 'calls')?.tabs;
            if (existingCustomerData) return;
            setLoadingTbasData(true)
            try {
                let endpoint = `${MainDomain}orders/${isOpen?.id}/calls`;

                const response = await axios.get(endpoint,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    });

                if (response.data.response !== "success") {
                    throw new Error("Failed to fetch data");
                }
                setSectionCounts(prevCounts => ({
                    ...prevCounts,
                    calls: response.data.count,
                }))
                setSections(prevSections =>
                    prevSections.map(section =>
                        section.key === 'calls'
                            ? { ...section, tabs: response.data.result }
                            : section
                    )
                );

            } catch (error) {
                console.error(`Error fetching order ${isOpen?.id} data:`, error);
                return {};
            } finally {
                setLoadingTbasData(false)
            }
        }
        const loadConfirmationData = async () => {
            const existingCustomerData = Sections.find(section => section.key === 'confirmation')?.tabs;
            if (existingCustomerData) return;
            setLoadingTbasData(true)
            try {
                let endpoint = `${MainDomain}orders/${isOpen?.id}/confirmation`;

                const response = await axios.get(endpoint,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    });

                if (response.data.response !== "success") {
                    throw new Error("Failed to fetch data");
                }
                setSectionCounts(prevCounts => ({
                    ...prevCounts,
                    confirmation: response.data.count,
                }))
                setSections(prevSections =>
                    prevSections.map(section =>
                        section.key === 'confirmation'
                            ? { ...section, tabs: response.data.result }
                            : section
                    )
                );

            } catch (error) {
                console.error(`Error fetching order ${isOpen?.id} data:`, error);
                return {};
            } finally {
                setLoadingTbasData(false)
            }
        }
        const loadShippingData = async () => {
            const existingCustomerData = Sections.find(section => section.key === 'shipping')?.tabs;
            if (existingCustomerData) return;
            setLoadingTbasData(true)
            try {
                let endpoint = `${MainDomain}orders/${isOpen?.id}/shipping`;

                const response = await axios.get(endpoint,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    });

                if (response.data.response !== "success") {
                    throw new Error("Failed to fetch data");
                }
                setSectionCounts(prevCounts => ({
                    ...prevCounts,
                    shipping: response.data.count,
                }))
                setSections(prevSections =>
                    prevSections.map(section =>
                        section.key === 'shipping'
                            ? { ...section, tabs: response.data.result }
                            : section
                    )
                );

            } catch (error) {
                console.error(`Error fetching order ${isOpen?.id} data:`, error);
                return {};
            } finally {
                setLoadingTbasData(false)
            }
        }
        const loadFollowupData = async () => {
            const existingCustomerData = Sections.find(section => section.key === 'followup')?.tabs;
            if (existingCustomerData) return;
            setLoadingTbasData(true)
            try {
                let endpoint = `${MainDomain}orders/${isOpen?.id}/followup`;

                const response = await axios.get(endpoint,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    });

                if (response.data.response !== "success") {
                    throw new Error("Failed to fetch data");
                }


                setSectionCounts(prevCounts => ({
                    ...prevCounts,
                    followup: response.data.count,
                }))
                setSections(prevSections =>
                    prevSections.map(section =>
                        section.key === 'followup'
                            ? { ...section, tabs: response.data.result }
                            : section
                    )
                );


            } catch (error) {
                console.error(`Error fetching order ${isOpen?.id} data:`, error);
                return {};
            } finally {
                setLoadingTbasData(false)
            }
        }
        switch (selectedTab) {
            case 'information':
                loadInfoData()
                break;
            case 'customer':
                loadCustomerData()
                break;
            case 'products':
                loadProductsData()
                break;
            case 'calls':
                loadCallsData()
                break;
            case 'confirmation':
                loadConfirmationData()
                break;
            case 'shipping':
                loadShippingData()
                break;
            case 'followup':
                loadFollowupData()
                break;
            default:
                // Handle default case if needed
                break;
        }
    }, [selectedTab, isOpen])

    const Columns = [
        {
            key: 'products', columns: [{ key: 'name', label: 'Product Name' },
            { key: 'sku', label: 'SKU' },
            { key: 'quantity', label: 'QTY' },
            { key: 'price', label: 'Price' },
            { key: 'size', label: 'Size' },
            { key: 'color', label: 'Color' }]
        },
        {
            key: 'calls', columns: [{ key: 'date', label: 'Date' },
            { key: 'answer', label: 'Answer' },
            { key: 'comment', label: 'Comment' }]
        },
        {
            key: 'confirmation', columns: [{ key: 'date', label: 'Date' },
            { key: 'status', label: 'Status' }]
        },
        {
            key: 'shipping', columns: [{ key: 'date', label: 'Date' },
            { key: 'status', label: 'Status' }]
        },
        {
            key: 'followup',
            columns:
                [{ key: 'date', label: 'Date' },
                { key: 'feedback', label: 'FeedBack' },
                { key: 'scheduleDate', label: 'Schedule Date' },
                { key: 'comment', label: 'Comment' },
                ]
        }
    ]

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={handleOnClose}
            width="max-w-6xl"
            height="h-[80%]"
            showHeader={true}
            showFooter={false}
            title={`Order N° ${order ? order.orderNum : '...'}`}
            headerClassName="px-0 py-4 border-b"
            bodyClassName="p-0"

        >
            <div className="w-full max-w-6xl rounded-lg overflow-hidden">


                {/* Tabs Section */}
                <Tabs
                    selectedKey={selectedTab} onSelectionChange={setSelectedTab}
                    aria-label="Order details"
                    className="p-0"
                    color="primary"
                    variant="bordered"
                    classNames={{
                        tabList: "gap-4 w-fit mx-auto relative p-4 border-0",
                        cursor: "rounded-full bg-info",
                        tab: "h-10 px-4 flex items-center gap-2",
                        tabContent: "group-data-[selected=true]:text-white",
                        base: " mx-auto block w-full hide-scrollbar"
                    }}
                >
                    {Sections.filter(s => s.permission ? hasPermission(s.permission) : s).map((t) => (
                        <Tab
                            key={t.key}
                            title={
                                <div className="flex items-center gap-2">
                                    <t.icon size={20} />
                                    <span className="hidden lg:block group-data-[selected=true]:block group-data-[selected=false]:hidden">
                                        {t.label}
                                    </span>
                                    {!['information', 'customer'].includes(t.key) &&

                                        <span className="text-xs text-white bg-glb_red rounded-full px-2 py-1">
                                            {loadingTbasData ? <motion.div
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: [0, 1, 0] }} // fade in and out
                                                transition={{
                                                    duration: 1,
                                                    repeat: Infinity,
                                                    repeatType: "loop",
                                                    repeatDelay: 0.5,

                                                }}
                                                className="h-2 w-2 bg-black dark:bg-white rounded"
                                            /> : sectionCounts?.[t.key] ?? 0}
                                        </span>}
                                </div>
                            }
                        >
                            <div className="px-0 lg:px-0 py-2 rounded-2xl ">
                                <div className="grid grid-cols-1 bg-black/5  rounded-2xl overflow-hidden">

                                    <div className="tabs-container ">
                                        {['information', 'customer'].includes(t.key) ? loadingTbasData ?
                                            Array.from({ length: 13 }).map((_, rowIndex) => (
                                                <motion.div
                                                    key={rowIndex}
                                                    initial={{ opacity: 0 }}
                                                    animate={{ opacity: [0, 1, 0] }} // fade in and out
                                                    transition={{
                                                        duration: 1,
                                                        repeat: Infinity,
                                                        repeatType: "loop",
                                                        repeatDelay: 0.5,
                                                        delay: rowIndex * 0.1,
                                                    }}
                                                    className=" py-4 px-2 h-12 lg:px-16 flex justify-between items-center font-medium text-center bg-gray-50 dark:bg-white/5 odd:bg-black/5 dark:odd:bg-white/10"
                                                >
                                                    {Array.from({ length: 2 }).map((_, colIndex) => (<motion.div
                                                        key={colIndex}
                                                        initial={{ opacity: 0 }}
                                                        animate={{ opacity: [0, 1, 0] }} // fade in and out
                                                        transition={{
                                                            duration: 1,
                                                            repeat: Infinity,
                                                            repeatType: "loop",
                                                            repeatDelay: 0.5,
                                                            delay: colIndex * 0.1,
                                                        }}
                                                        className=" py-4 rounded-lg lg:px-16 flex justify-between items-center font-medium text-center bg-black/50 dark:bg-white/50"
                                                    />))}
                                                </motion.div>

                                            )) : t.tabs && Object.entries(t.tabs).map(([label, value], idx) => (
                                                <React.Fragment key={idx}>
                                                    {renderInfoCell(label, value)}
                                                </React.Fragment>
                                            )) : <TableComponent
                                            loading={loadingTbasData}
                                            columns={Columns.find((col) => col.key === t.key).columns}
                                            data={t.tabs}
                                            renderCell={renderTableCell}
                                            rowClassNames={tableRowClassNames}
                                        />

                                        }
                                    </div>


                                </div>
                            </div>
                        </Tab>
                    ))}


                </Tabs>
            </div>
        </CustomModal>
    );
};

export default OrderInformationModal;