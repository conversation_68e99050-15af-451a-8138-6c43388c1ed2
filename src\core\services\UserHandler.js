export const getUserPermissions = () => {
    return localStorage.getItem("xM_htUju") ? JSON.parse(localStorage.getItem("xM_htUju")).permissions || [] : [];
};

export const getUserInfos = () => {

    const data = localStorage.getItem("xM_htUju");
    if (!data) return null;
    const parsed = JSON.parse(data);
    if (parsed === undefined) {
        // Disconnect user by removing tokens and dispatching auth change event
        localStorage.removeItem("xM_htkju");
        return null;
    }
    return parsed;

}