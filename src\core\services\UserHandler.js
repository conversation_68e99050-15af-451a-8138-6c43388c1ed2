export const getUserPermissions = () => {
    const data = localStorage.getItem("xM_htUju");

    if (!data || data === null || data === undefined) {
        return [];
    }

    try {
        const parsed = JSON.parse(data);
        return parsed?.permissions || [];
    } catch (error) {
        console.error("Failed to parse user data for permissions:", error);
        // Remove corrupted data and logout user
        localStorage.removeItem("xM_htkju");
        return [];
    }
};

export const getUserInfos = () => {

    const data = localStorage.getItem("xM_htUju");

    if (!data || data === null || data === undefined) {
        // Disconnect user by removing tokens and dispatching auth change event
        localStorage.removeItem("xM_htkju");
        return null;
    }

    try {
        const parsed = JSON.parse(data);
        if (!parsed || parsed === undefined) {
            // Disconnect user by removing tokens and dispatching auth change event
            localStorage.removeItem("xM_htkju");
            return null;
        }
        return parsed;
    } catch (error) {
        // If JSON parsing fails, also logout the user
        console.error("Failed to parse user data:", error);
        localStorage.removeItem("xM_htkju");
        return null;
    }

}