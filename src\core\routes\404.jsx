import React from 'react';
import { Link } from 'react-router-dom';
import { useThemeProvider } from '../providers/ThemeContext';
import { RouteNames } from './routes';
import BrandLogo from '../../modules/shared/assets/images/logo-png.png';
import codPowerGroupLogoDark from '../../modules/shared/assets/images/logo-png-dark.png';

export default function NotFoundPage() {
    const { currentTheme } = useThemeProvider();

    return (
        <div className="w-screen h-screen bg-base_light dark:bg-dark-gradient flex flex-col items-center justify-center">
            <div className="text-center">
                {/* Logo */}
                <div className="mb-8">
                    <Link to={RouteNames.dashboard}>
                        <img
                            src={currentTheme === 'light' ? BrandLogo : codPowerGroupLogoDark}
                            className="w-48 mx-auto"
                            alt="Logo"
                        />
                    </Link>
                </div>

                {/* 404 Message */}
                <div className="space-y-4">
                    <h1 className="text-6xl font-bold text-gray-800 dark:text-white">404</h1>
                    <h2 className="text-2xl font-semibold text-gray-600 dark:text-gray-300">Page Not Found</h2>
                    <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
                        The page you're looking for doesn't exist or has been moved.
                    </p>

                    {/* Back to Dashboard Button */}
                    <div className="mt-8">
                        <Link
                            to={RouteNames.dashboard}
                            className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
                        >
                            Back to Dashboard
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}