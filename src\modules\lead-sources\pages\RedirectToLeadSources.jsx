import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { RouteNames } from '../../../core/routes/routes';
import { useDispatch } from 'react-redux';
import { fetchDropifyData } from '../../../core/redux/slices/leadSource/dropifySlice';
import BlankLayout from '../../shared/layouts/BlankLayout';
import { Spinner } from '@heroui/react';

function RedirectToLeadSources() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [checking, setChecking] = useState(false)

    useEffect(() => {
        // Create an async function inside the useEffect
        const checkDropify = async (clientId, redirectUri) => {
            // Dispatch the fetchDropifyData action
            setChecking(true)
            const res = await dispatch(fetchDropifyData({
                clientId,
                redirectUri
            }));

            // Check if the action was fulfilled
            if (fetchDropifyData.fulfilled.match(res)) {
                setChecking(false)
                return true;
            } else {
                setChecking(false)
                console.error("Error fetching Dropify data:", res.payload);
                return false;
            }

        };

        // Extract query parameters from the URL
        const searchParams = new URLSearchParams(window.location.search);
        const clientId = searchParams.get("client_id");
        const redirectUri = searchParams.get("redirect_uri");

        // Check if both parameters exist
        const redirectUser = async () => {
            if (clientId && redirectUri) {
                // Call the checkDropify function and wait for the result
                const res = await checkDropify(clientId, redirectUri);
                if (!res) {
                    navigate(RouteNames.dashboard);
                } else {
                    navigate(`${RouteNames.leadSources}?is_dropify=1&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}`);
                }
            } else {
                // If the parameters are missing, navigate to a fallback (e.g., dashboard)
                navigate(RouteNames.dashboard);
            }
        };

        // Call the redirectUser function
        redirectUser();

    }, [navigate, dispatch]);

    return <div className='w-screen h-screen flex justify-center items-center'><Spinner size='lg' /></div>; // Optionally show a loading message
}

export default RedirectToLeadSources;
