import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { followupStatusUrl, ordersExportUrl, ordersListUrl, orderStatusUrl } from "../URLs";
import { getToken } from "../../../services/TokenHandler";
import axios from "axios";
import { CreateOrdersExcel } from "../../../services/ExcelHandler";
import { setOpenFilterModal } from "../contentSlice";
import { get } from "lodash";
import { toast } from "sonner";


const initialState = {
    orders: [],
    followup: [],
    orderStatus: [],
    followupStatus: [],
    params: {
        startDate: null,
        endDate: null,
        status: null,
        productReference: null,
        sortBy: null,
        paymentMethod: null,
        consigneeContact: null,
        consigneePhone: null,
        originCountry: null,
        destinationCountry: null,
        orderNum: null,
        followup: null,
        followupStatus: null,
        page: 1,
        excludedStatus: [],
        listStatus: [],
        listeFollowupStatus: [],
        excludedFollowupStatus: [],
        upsell: null,
    },
    tabsTrigger: false,
    paramsCount: 0,
    loading: false,
    statusLoading: false,
    paramsLoading: false,
    error: null,
    exportLoading: false,
    exportError: null,
};

export const updateParams = createAsyncThunk(
    "orders/updateParams",
    async (newParams, { getState, rejectWithValue }) => {
        try {

            Object.keys(newParams).forEach(key => {
                if (newParams[key] === '') {
                    newParams[key] = null;
                }
            });
            const { params } = getState().orders;
            if (!newParams.hasOwnProperty('page')) {
                newParams.page = 1;
            }
            return { ...params, ...newParams };
        } catch (error) {
            return rejectWithValue("Failed to update params");
        }
    }
);

export const resetParams = createAsyncThunk(
    "orders/resetParams",
    async (_, { getState, rejectWithValue }) => {
        try {
            const { params } = getState().orders;
            console.log('fsefse');

            const resetParams = Object.keys(params).reduce((acc, key) => {
                if (key !== 'followup' && key !== 'page') {
                    acc[key] = null;
                }
                acc.page = 1;
                return acc;
            }, {});
            return resetParams;
        } catch (error) {
            return rejectWithValue("Failed to reset params");
        }
    }
);


// Async thunk for installing Shopify app
export const getOrders = createAsyncThunk(
    "orders/get",
    async (_, { getState, rejectWithValue }) => {
        try {
            const { params } = getState().orders;
            let query = `${ordersListUrl}?page=${params.page}`;
            params.startDate && (query += `&startDate=${params.startDate}`);
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.status && (query += `&status=${params.status === 'all' ? '' : params.status}`);
            params.productReference && (query += `&productReference=${params.productReference}`);
            params.sortBy && (query += `&dateType=${params.sortBy}`);
            params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`);
            params.consigneeContact && (query += `&consigneeContact=${params.consigneeContact}`);
            params.consigneePhone && (query += `&consigneePhone=${params.consigneePhone}`);
            params.originCountry && (query += `&originCountry=${params.originCountry}`);
            params.destinationCountry && (query += `&destinationCountry=${params.destinationCountry}`);
            params.orderNum && (query += `&keyword=${params.orderNum}`);
            params.followup && (query += `&followup=0`);
            params.followupStatus && (query += `&followupStatus=${params.followupStatus}`);
            params.upsell && (query += `&upsell=${params.upsell}`);
            // Add this block for excludedStatus
            if (Array.isArray(params.excludedStatus) && params.excludedStatus.length > 0) {
                params.excludedStatus.forEach(status => {
                    if (status) query += `&excludedStatus[]=${encodeURIComponent(status)}`;
                });
            }
            // Add this block for listStatus
            if (Array.isArray(params.listStatus) && params.listStatus.length > 0) {
                params.listStatus.forEach(status => {
                    if (status) query += `&listStatus[]=${encodeURIComponent(status)}`;
                });
            }
            console.log(query);

            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }

            return response.data;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getStaticOrders = createAsyncThunk(
    "staticOrders/get",
    async ({ tab = null, currentPage = 1, isFollowUp = false }, { rejectWithValue }) => {
        try {
            let query = `${ordersListUrl}?page=${currentPage}`;
            query += `&status=${tab === 'all' ? '' : tab}`;
            if (isFollowUp) {
                query += `&followup=1`;
            }

            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }

            return response.data;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getFollowup = createAsyncThunk(
    "followup/get",
    async (tab = null, { getState, rejectWithValue }) => {
        try {
            const { params } = getState().orders;
            let query = `${ordersListUrl}?page=${params.page}&followup=1`;

            // Use the tab parameter if provided, otherwise use params.status
            tab && tab !== 'all' ? (query += `&followupStatus=${tab}`) :
                params.followupStatus && (query += `&followupStatus=${params.followupStatus === 'all' ? '' : params.followupStatus}`);

            params.startDate && (query += `&startDate=${params.startDate}`); // in yyyy-mm-dd format
            params.endDate && (query += `&endDate=${params.endDate}`); // in yyyy-mm-dd format
            params.productReference && (query += `&productReference=${params.productReference}`); // product id
            params.sortBy && (query += `&dateType=${params.sortBy}`);
            params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`);
            // Add this block for excludedFollowupStatus
            if (Array.isArray(params.excludedFollowupStatus) && params.excludedFollowupStatus.length > 0) {
                params.excludedFollowupStatus.forEach(status => {
                    if (status) query += `&excludedFollowupStatus[]=${encodeURIComponent(status)}`;
                });
            }
            // Add this block for listeFollowupStatus
            if (Array.isArray(params.listeFollowupStatus) && params.listeFollowupStatus.length > 0) {
                params.listeFollowupStatus.forEach(status => {
                    if (status) query += `&listeFollowupStatus[]=${encodeURIComponent(status)}`;
                });
            }


            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }

            return response.data;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getExportedOrders = createAsyncThunk(
    "ordersExport/get",
    async (selected = [], { getState, dispatch, rejectWithValue }) => {
        try {
            const { params } = getState().orders;

            if (Array.isArray(selected) && selected.length > 0) {
                console.log('hee');

                let subQuery = `${ordersExportUrl}?page=${params.page}`;
                // Add this block for listStatus
                selected.forEach(id => {
                    if (id) subQuery += `&ids[]=${id}`; //url params is ids[]
                });

                const response = await axios.get(subQuery, {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                });

                if (response.data.response !== 'success') {
                    return rejectWithValue({
                        status: response.data?.status,
                        message: response.data.message || 'Error fetching orders'
                    });
                }
                const transformedData = response.data.result.map(item => Object.values(item));
                // Now that all data has been collected, generate the Excel
                await CreateOrdersExcel(transformedData);  // Using excelData instead of response.data.result
                return response.data;
            } else {

                let query = `${ordersExportUrl}?page=${params.page}`;

                params.startDate && (query += `&startDate=${params.startDate}`); //url params is d=dd-mm-yyyy,dd-mm-yyyy
                params.endDate && (query += `&endDate=${params.endDate}`);
                params.status && (query += `&status=${params.status === 'all' ? '' : params.status}`); //url params is status
                params.productReference && (query += `&productReference=${params.productReference}`); // product id | url params is product
                params.sortBy && (query += `&dateType=${params.sortBy}`); //url params is dateType
                params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`); //url params is pm
                params.consigneeContact && (query += `&consigneeContact=${params.consigneeContact}`);
                params.consigneePhone && (query += `&consigneePhone=${params.consigneePhone}`);
                params.originCountry && (query += `&originCountry=${params.originCountry}`); //url params is oc
                params.destinationCountry && (query += `&destinationCountry=${params.destinationCountry}`); //url params is dc
                params.orderNum && (query += `&keyword=${params.orderNum}`); //url params is k
                params.followup && (query += `&followup=${params.followup}`); //url params is followup
                params.followupStatus && (query += `&followupStatus=${params.followupStatus}`); //url params is followupS
                params.upsell && (query += `&upsell=${params.upsell}`); //url params is upsell
                // Add this block for excludedStatus
                if (Array.isArray(params.excludedStatus) && params.excludedStatus.length > 0) {
                    params.excludedStatus.forEach(status => {
                        if (status) query += `&excludedStatus[]=${encodeURIComponent(status)}`; //url params is ex[]
                    });
                }
                // Add this block for listStatus
                if (Array.isArray(params.listStatus) && params.listStatus.length > 0) {
                    params.listStatus.forEach(status => {
                        if (status) query += `&listStatus[]=${encodeURIComponent(status)}`; //url params is listS[]
                    });
                }
                const response = await axios.get(query, {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                });
                if (response.data.response !== 'success') {
                    dispatch(setOpenFilterModal(true));
                    return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
                }
                const transformedData = response.data.result.map(item => Object.values(item));
                // Now that all data has been collected, generate the Excel
                await CreateOrdersExcel(transformedData);
                return response.data;
            }



        } catch (error) {
            console.log(error);

            dispatch(setOpenFilterModal(true));
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getStatusCount = createAsyncThunk(
    "ordersStatusCount/get",
    async (orderStatus, { getState, dispatch, rejectWithValue }) => {


        try {
            const { params, } = getState().orders;

            if (params.status && params.status !== orderStatus && orderStatus !== 'all') return 0;



            let query = `${ordersListUrl}?count=1&page=${params.page}&status=${orderStatus !== 'all' ? orderStatus : params.status || ''}`;
            params.startDate && (query += `&startDate=${params.startDate}`); // in yyyy-mm-dd format
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.productReference && (query += `&productReference=${params.productReference}`); // product id
            params.sortBy && (query += `&dateType=${params.sortBy}`);
            params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`);
            params.consigneeContact && (query += `&consigneeContact=${params.consigneeContact}`); // full name
            params.consigneePhone && (query += `&consigneePhone=${params.consigneePhone}`);
            params.originCountry && (query += `&originCountry=${params.originCountry}`);
            params.destinationCountry && (query += `&destinationCountry=${params.destinationCountry}`);
            params.orderNum && (query += `&keyword=${params.orderNum}`);


            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }

            return response.data.result;
        } catch (error) {
            //     dispatch(setOpenFilterModal(true));
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getFollowupStatusCount = createAsyncThunk(
    "followupStatusCount/get",
    async (followupStatus, { dispatch, getState, rejectWithValue }) => {
        try {
            const { params } = getState().orders;

            console.log('followupStatus', followupStatus, params.followupStatus);

            if (params.followupStatus && params.followupStatus !== followupStatus && followupStatus !== 'all') return 0;


            let query = `${ordersListUrl}?count=1&followup=1&followupStatus=${followupStatus !== 'all' ? followupStatus : params.followupStatus || ''}`;
            params.startDate && (query += `&startDate=${params.startDate}`); // in yyyy-mm-dd format
            params.endDate && (query += `&endDate=${params.endDate}`);
            params.productReference && (query += `&productReference=${params.productReference}`); // product id
            params.sortBy && (query += `&dateType=${params.sortBy}`);
            params.paymentMethod && (query += `&paymentMethod=${params.paymentMethod}`);
            params.consigneeContact && (query += `&consigneeContact=${params.consigneeContact}`); // full name
            params.consigneePhone && (query += `&consigneePhone=${params.consigneePhone}`);
            params.originCountry && (query += `&originCountry=${params.originCountry}`);
            params.destinationCountry && (query += `&destinationCountry=${params.destinationCountry}`);
            params.orderNum && (query += `&keyword=${params.orderNum}`);

            console.log('query', query);

            const response = await axios.get(query, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders' });
            }


            return response.data.result;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getOrderStatus = createAsyncThunk(
    "orderStatus/get",
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get(`${orderStatusUrl}`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders status' });
            }

            return response.data;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);
// Async thunk for installing Shopify app
export const getFollowupStatus = createAsyncThunk(
    "followuoStatus/get",
    async (_, { rejectWithValue }) => {
        try {
            const response = await axios.get(`${followupStatusUrl}`,
                {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                }
            );

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching orders status' });
            }

            return response.data;
        } catch (error) {
            return rejectWithValue({ status: error.response?.status, message: error.response?.data?.message || error.message });
        }
    }
);


const ordersManagementSlice = createSlice({
    name: 'orders',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(getOrders.pending, (state) => {
                state.loading = true;
                state.error = null;
                state.exportError = null;
            })
            .addCase(getOrders.fulfilled, (state, action) => {
                state.loading = false;
                state.orders = action.payload;

            })
            .addCase(getOrders.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(getStaticOrders.pending, (state) => {
                state.loading = true;
                state.error = null;
                state.exportError = null;
            })
            .addCase(getStaticOrders.fulfilled, (state, action) => {
                state.loading = false;
                state.orders = action.payload;
            })
            .addCase(getStaticOrders.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            //Followup
            .addCase(getFollowup.pending, (state) => {
                state.loading = true;
                state.error = null;
                state.exportError = null;
            })
            .addCase(getFollowup.fulfilled, (state, action) => {
                state.loading = false;
                state.followup = action.payload;
            })
            .addCase(getFollowup.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;

            })
            // Order status
            .addCase(getOrderStatus.pending, (state) => {
                state.statusLoading = true;
                state.error = null;
                state.exportError = null;
            })
            .addCase(getOrderStatus.fulfilled, (state, action) => {

                const { result, orderStatusesInMenu } = action.payload;

                // // Filter result to include only the keys present in orderStatusesInMenu
                // const filteredResult = Object.keys(result)
                //     .filter(key => orderStatusesInMenu.includes(key))
                //     .reduce((acc, key) => {
                //         acc[key] = result[key];
                //         return acc;
                //     }, {});

                state.orderStatus = result;
                state.statusLoading = false;
            })
            .addCase(getOrderStatus.rejected, (state, action) => {
                state.statusLoading = false;
                state.error = action.payload;
            })
            // Follow up status
            .addCase(getFollowupStatus.pending, (state) => {
                state.statusLoading = true;
                state.error = null;
                state.exportError = null;
            })
            .addCase(getFollowupStatus.fulfilled, (state, action) => {

                const { result, followupStatusesInMenu } = action.payload;

                // Filter result to include only the keys present in followupStatusesInMenu
                const filteredResult = Object.keys(result)
                    .filter(key => followupStatusesInMenu.includes(key))
                    .reduce((acc, key) => {
                        acc[key] = result[key];
                        return acc;
                    }, {});

                state.followupStatus = filteredResult;
                state.statusLoading = false;
            })
            .addCase(getFollowupStatus.rejected, (state, action) => {
                state.statusLoading = false;
                state.error = action.payload;
            })
            .addCase(getExportedOrders.pending, (state) => {
                state.exportLoading = true;
                state.exportError = null;
            })
            .addCase(getExportedOrders.fulfilled, (state, action) => {
                state.exportLoading = false;
                toast.success("Export successful!");
            })
            .addCase(getExportedOrders.rejected, (state, action) => {
                state.exportLoading = false;
                state.exportError = action.payload?.message;

            })
            .addCase(updateParams.pending, (state) => {
                state.paramsLoading = true;
                state.error = null;
                state.exportError = null;
            })
            .addCase(updateParams.fulfilled, (state, action) => {
                state.paramsLoading = false;
                state.params = action.payload;
                state.paramsCount = Object.keys(state.params).filter(key => {
                    const value = state.params[key];
                    return key !== 'page' &&
                        key !== 'followup' &&
                        value !== null &&
                        value !== '' &&
                        value !== 0 &&
                        !(Array.isArray(value) && value.length === 0);
                }).length;


            })
            .addCase(updateParams.rejected, (state, action) => {
                state.paramsLoading = false;
                state.error = action.payload;
            })
            .addCase(resetParams.pending, (state) => {
                state.paramsLoading = true;
                state.error = null;
                state.exportError = null;
            })
            .addCase(resetParams.fulfilled, (state, action) => {
                state.paramsLoading = false;
                state.params = action.payload;
                state.paramsCount = Object.keys(state.params).filter(key => {
                    const value = state.params[key];
                    return key !== 'page' &&
                        key !== 'followup' &&
                        value !== null &&
                        value !== '' &&
                        value !== 0 &&
                        !(Array.isArray(value) && value.length === 0);
                }).length;
                state.tabsTrigger = false;
            })
            .addCase(resetParams.rejected, (state, action) => {
                state.paramsLoading = false;
                state.error = action.payload;
            });
    },
});

export default ordersManagementSlice.reducer;
