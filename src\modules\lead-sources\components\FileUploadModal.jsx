import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { useDropzone } from "react-dropzone";
import { But<PERSON> } from "@heroui/button";
import {
  Cancel01Icon,
  CloudUploadIcon,
  File02Icon,
  FileDownloadIcon,
  Link03Icon,
  Logout02Icon,
  RepeatIcon,
  Unlink03Icon,
} from "hugeicons-react";
import { Progress } from "@heroui/progress";
import CustomModal from "@shared/components/CustomModal.jsx";
import { Link, useNavigate } from "react-router-dom";
import googlesheet from "@shared/assets/images/googlesheet.svg";
import excelSheet from "@shared/assets/images/excel.svg";
import shopify from "@shared/assets/images/shopify.svg";
import lightfunnels from "@shared/assets/images/lightfunnels.png";
import youcan from "@shared/assets/images/youcan.png";
import UnderlinedInput from "../../settings/components/UnderlinedInput";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Header } from "@heroui/card";
import { Divider, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger, Input, Spinner, Tooltip } from "@heroui/react";
import codPowerGroupLogo from "@shared/assets/images/cod-power-group-logo.svg";
import codPowerGroupLogoDark from "@shared/assets/images/cod-power-group-logo-dark.svg";
import { useThemeProvider } from "../../../core/providers/ThemeContext";
import { useDispatch, useSelector } from "react-redux";
import { installShopifyApp } from "../../../core/redux/slices/leadSource/shopifySlice";
import { installYouCanApp } from "../../../core/redux/slices/leadSource/youcanSlice";
import { installLightfApp } from "../../../core/redux/slices/leadSource/lightfSlice";
import { importOrderFromGoogleSheet, installGoogleSheet } from "../../../core/redux/slices/leadSource/googleSheetSlice";
import { importExcelFile, resetImportState } from "../../../core/redux/slices/leadSource/uploadOrdersFileSlice";
import { toast } from "sonner";
import { RouteNames } from "../../../core/routes/routes";
import { DeleteGoogleSheetAccountUrl } from "../../../core/redux/slices/URLs";
import axios from "axios";
import { getToken } from "../../../core/services/TokenHandler";
import uploadFileSVG from "@shared/assets/images/fileUpload.svg";
import { getModels } from "../../../core/redux/slices/general/generalSlice";
import { getLeadSourceList } from "../../../core/redux/slices/leadSource/leadSourcesSlice";




const FileUploadModal = ({ isOpen, setIsOpen, redirected }) => {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const isCancelledRef = useRef(false);


  const [fileUploadedIndex, setFileUploadedIndex] = useState(0)
  const [isLinked, setIsLinked] = useState(false);
  const [shopifyInputOpen, setshopifyInputOpen] = useState(false)
  const [shopifyStore, setShopifyStore] = useState("");
  const { currentTheme } = useThemeProvider();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading: shopifyLoading, installUrl, error: shopifyError } = useSelector((state) => state.shopify);
  const { loading: youcanLoading, error: youcanError } = useSelector((state) => state.youcan);
  const { loading: lightfLoading, error: lightfError } = useSelector((state) => state.lightf);
  const { loading: googleSheetLoading, error: googleSheetError } = useSelector((state) => state.googleSheet);
  const { loading: uploadLoading, error: errorUpload } = useSelector((state) => state.OrdersImport);
  const { loading: generalLoading, error: errorGeneral, models } = useSelector((state) => state.general);
  const [loadingSheets, setLoadingSheets] = useState({});
  const inputRef = useRef(null);
  const [curretnTab, setCurretnTab] = useState(1);
  const [sheets, setSheets] = useState(null)
  const [deleteingLoaidng, setDeleteingLoaidng] = useState(false)
  const [selectedDataSheet, setSelectedDataSheet] = useState([])

  useEffect(() => {
    setIsUploading(uploadLoading)
  }, [uploadLoading])


  const onDrop = useCallback(async (acceptedFiles) => {
    if (acceptedFiles?.length) {
      const file = acceptedFiles[0];


      // Start upload
      const result = await dispatch(importExcelFile(file));

      if (importExcelFile.fulfilled.match(result)) {

        navigate(RouteNames.allOrders);
        onClose();
      }

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setUploadProgress(progress);
        if (progress >= 100) {
          clearInterval(interval);
        }
      }, 100);
    }
  }, [dispatch, navigate, setIsOpen]);

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
      "application/vnd.ms-excel": [".xls"],
      "text/csv": [".csv"],
    },
    maxSize: 10485760, // 10MB
  });

  const handleShopifyLink = () => {
    setIsLinked(!isLinked);
  };

  const handleStoreName = (storeName) => {
    const sanitizedStoreName = storeName.replace(/[.,\s]/g, "");
    setShopifyStore(sanitizedStoreName);

  }

  const modalRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setShopifyStore('');
        setshopifyInputOpen(false)
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };

  }, []);

  const handleShopifyConnect = async () => {
    const resultAction = await dispatch(installShopifyApp({ shop: shopifyStore + '.myshopify.com' }));
    if (installShopifyApp.fulfilled.match(resultAction)) {
      const url = resultAction.payload; // 👈 get the installUrl directly
      window.open(url, "_blank");
      setShopifyStore('');
      setshopifyInputOpen(false)
      onClose()
    }

  }
  const handleYouCanConnect = async () => {
    const resultAction = await dispatch(installYouCanApp());
    if (installYouCanApp.fulfilled.match(resultAction)) {
      const url = resultAction.payload; // 👈 get the installUrl directly
      window.open(url, "_blank");

      onClose()
    }

  }
  const handleLightFConnect = async () => {

    const resultAction = await dispatch(installLightfApp());
    if (installLightfApp.fulfilled.match(resultAction)) {
      const url = resultAction.payload; // 👈 get the installUrl directly
      window.open(url, "_blank");

      onClose()
    }

  }

  useEffect(() => {

    if (redirected) {
      handleGoogleSheetConnect()

    }
  }, [dispatch, redirected])


  const handleGoogleSheetConnect = async () => {
    const resultAction = await dispatch(installGoogleSheet());
    if (installGoogleSheet.fulfilled.match(resultAction)) {

      if (resultAction.payload.actionType === 'spreadSheet') {

        setSheets(resultAction.payload.result);
        setCurretnTab(2);
      }
      else {
        const url = resultAction.payload.url; // 👈 get the installUrl directly
        window.open(url, "_blank");

        onClose()

      }


    }


  }
  const handleImportOrderFromGSheet = async () => {
    setIsUploading(true);
    setUploadProgress(0);
    setFileUploadedIndex(0);
    isCancelledRef.current = false;

    const totalSheets = selectedDataSheet.length;
    let completedSheets = 0;


    for (const [index, sheet] of selectedDataSheet.entries()) {
      if (isCancelledRef.current) {
        // Stop the upload process if canceled
        toast.error("Import process cancelled");
        break;
      }

      try {
        const result = await dispatch(importOrderFromGoogleSheet({ spreadSheetId: sheet.id, sheetName: sheet.name }));
        if (importOrderFromGoogleSheet.fulfilled.match(result)) {
          if (result.payload.response === "error") {
            toast.error(result.payload.message || "Can't import orders from this sheet", {
              description: 'Please check your sheet',
            });
          } else {
            completedSheets++;
            setUploadProgress(Math.round((completedSheets / totalSheets) * 100));
            setFileUploadedIndex(index + 1); // Update the current file index
          }
        } else {
          console.log(result);
        }
      } catch (error) {
        console.error(error);
      }
    }

    if (!isCancelledRef.current) {
      // Ensure progress reaches 100% after the loop finishes
      setUploadProgress(100);
      setFileUploadedIndex(totalSheets); // Set the last file index

      // Add a small delay to allow the UI to update before closing the modal
      await new Promise((resolve) => setTimeout(resolve, 500));
      toast.success(`Sheets imported successfully`);
      onClose();
      navigate(RouteNames.allOrders);
    } else {
      setIsUploading(false); // Reset uploading state if canceled
    }
  };




  const DeleteGoogleAccount = async () => {
    try {
      setDeleteingLoaidng(true)
      // Call the API to store the state in session (GET request with query params)
      const response = await axios.delete(DeleteGoogleSheetAccountUrl, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      if (response.data.response === "error") {
        toast.error("Can't delete this google account");

      }
      else {
        dispatch(getLeadSourceList({ page: 1 }));
        toast.success("Google account deleted successfully", {
          description: 'Re-connect to Google Sheet',
        }
        );
        setCurretnTab(1);
        setSheets(null)
      }

    } catch (error) {
      console.log(error);
      toast.error("Can't delete this google account");
    } finally {
      setDeleteingLoaidng(false)
    }
  };

  const onClose = () => {
    setIsOpen(false);
    setCurretnTab(1)
    setSelectedDataSheet([])
  }
  const CancelUpload = () => {
    console.log('cancel');
    isCancelledRef.current = true; // Update the ref value
    setIsUploading(false);
    toast.error("Import process cancelled");
  }

  useEffect(() => {
    if (isOpen) {
      dispatch(getModels());
    }
  }, [isOpen, dispatch]);


  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      width="max-w-6xl"
      showHeader={false}
      bodyClassName="p-8"
      showBlur={shopifyInputOpen}
      onBlurContent={(<Card ref={modalRef} className=" bg-white dark:bg-base_card
            border dark:border-[#ffffff10]
            border-[#********]
            overflow-y-auto shadow-lg min-w-[360px] w-[60%]">
        <CardHeader className="flex gap-3 justify-center items-center">
          {currentTheme === "light" ? (
            <img src={codPowerGroupLogo} alt="power group world logo" className="w-20" />
          ) : (
            <img src={codPowerGroupLogoDark} alt="power group world logo" className="w-20" />
          )}
          <RepeatIcon size={24} />
          <img src={shopify} alt="shopify logo" className="w-16" />
        </CardHeader>
        <Divider />
        <CardBody className="flex flex-col gap-3 justify-center items-center py-12">
          <p className="text-center text-lg">
            Please enter your full Shopify store name </p>
          <div className=" max-w-[400px] gap-1 flex flex-col w-full justify-start items-start">
            <div className=" w-full flex justify-end items-end">
              <Input
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleShopifyConnect();
                  }
                }}
                value={shopifyStore}
                onChange={(e) => handleStoreName(e.target.value)}
                label="Store"
                variant="underlined"
                color={'primary'}
                classNames={{
                  label: ["!text-[#00000050] dark:!text-[#FFFFFF30]"],
                }}

              />
              <p className="text-center text-lg">
                .myshopify.com </p>
            </div>
            <p className="text-center text-sm text-glb_red">
              {shopifyError && shopifyError}</p>
          </div>

        </CardBody>
        <Divider />
        <CardFooter className="flex gap-2 justify-end items-end">
          <Button onClick={() => { setShopifyStore(''); setshopifyInputOpen(false) }} className="px-8 py-2 rounded-full bg-glb_red text-white flex flex-row items-center justify-center gap-1">
            <Cancel01Icon size={18} />
            Close
          </Button>
          <Button
            isLoading={shopifyLoading}
            onClick={() => handleShopifyConnect()}
            color="success"
            startContent={!shopifyLoading && <img alt="Continue with Shopify"
              src={shopify} className="h-6 w-6" />}
            className="px-8 py-2 rounded-full text-white bg-[#609C38] flex flex-row items-center justify-center gap-1"
          >
            Connect
          </Button>
        </CardFooter>
      </Card>)}
      footerContent={
        isUploading ?
          <Button
            variant="light"
            size="sm"
            className="mx-auto px-8 py-2 rounded-full bg-glb_blue text-white flex flex-row items-center justify-center gap-1"
            onClick={() => CancelUpload()}
          >
            Cancel
          </Button> : curretnTab === 1 ?
            <Button
              className="mx-auto px-8 py-2 rounded-full bg-glb_red text-white flex flex-row items-center justify-center gap-1"
              onClick={onClose}
            >
              <Cancel01Icon size={18} />
              Close
            </Button> :
            <div className="flex justify-between items-center flex-wrap max-w-5xl w-full mx-auto p-4">
              <Button
                className=" px-8 py-2 rounded-full bg-glb_red text-white flex flex-row items-center justify-center gap-1"
                onClick={onClose}
              >
                <Cancel01Icon size={18} />
                Close
              </Button>

              <Button
                isDisabled={selectedDataSheet.length === 0}
                className=" px-8 py-2 rounded-full bg-glb_blue text-white flex flex-row items-center justify-center gap-1"
                onClick={() => {
                  handleImportOrderFromGSheet()
                }}
              >
                Import Selection ({selectedDataSheet.length})
              </Button>
            </div>
      }
    >
      {curretnTab === 1 ?
        <div className="w-full  mx-auto p-4">
          <div className="w-full flex max-w-3xl mx-auto justify-between items-start flex-wrap">
            <div className="">
              <h2 className="text-xl text-start font-semibold mb-2">
                Importing your sheet file by Drag or Upload
              </h2>
              <div className="flex justify-between items-center  flex-wrap">
                <p className="text-sm text-gray-400 mb-4 font-light">
                  Select relevant documents to complete your computer
                </p>

              </div>
            </div>
            <div className="flex flex-col justify-start items-center gap-1 ">
              {generalLoading ? (
                <Spinner size="sm" />
              ) : (
                <Dropdown placement="bottom-end">
                  <DropdownTrigger>
                    <div className="text-glb_blue text-sm cursor-pointer flex justify-center items-center gap-1 min-w-32"><File02Icon size={18} />
                      <span>Get .XLSX Model</span></div>
                  </DropdownTrigger>
                  <DropdownMenu aria-label="Static Actions">

                    <DropdownItem key="copy"><Link

                      to={models && models["importOrdersFromGoogleSheet"] ? models["importOrdersFromGoogleSheet"] : "#"}
                      target="_blank"
                      className="w-full flex justify-start items-center gap-2"
                      variant="light"
                      size="sm"
                    >
                      <img src={googlesheet} className="h-6 w-6" />
                      <span><strong>Google</strong> Sheet Model</span>
                    </Link>
                    </DropdownItem>
                    <DropdownItem key="new">
                      <Link
                        to={models && models["importOrdersFromExcel"] ? models["importOrdersFromExcel"] : "#"}
                        target="_blank"
                        className="w-full flex justify-start items-center gap-2"
                        variant="light"
                        size="sm">
                        <img src={excelSheet} className="h-6 w-6" />
                        <span><strong>Excel</strong> Sheet Model</span>
                      </Link>
                    </DropdownItem>
                  </DropdownMenu>

                </Dropdown>

              )}


            </div>

          </div>

          <div className="w-full mx-auto max-w-2xl">



            {!isUploading && (

              <div
                {...getRootProps()}
                className={`border-2  border-dashed border-gray-700 rounded-lg p-8 my-6 text-center cursor-pointer ${isDragActive ? "border-glb_blue bg-info/5" : "border-gray-400"
                  }`}
              >
                <input id="file-upload-input"  {...getInputProps()} />
                <CloudUploadIcon className="mx-auto h-12 w-12 text-gray-600 mb-4" />
                <p className="text-lg font-medium mb-2">
                  Select a file or drag and drop here
                </p>
                <p className="text-sm font-light text-gray-500 mb-4">
                  Excel, Google Sheet, file size no more than 10MB
                </p>
                <Button
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent triggering the parent div's onClick
                    open(); // This opens the file dialog
                  }}
                  variant="solid"
                  className="text-white bg-[#0258E8] dark:bg-[#0258E880] hover:bg-glb_blue  rounded-full z-0"
                >
                  Select file from here
                </Button>
              </div>
            )}

            {isUploading && (
              <div className="text-center p-8 my-6 flex flex-col gap-1">
                <FileDownloadIcon className="mx-auto h-12 w-12 text-glb_blue mb-6" />
                <p className="font-medium">
                  Starting Uploading {uploadProgress}%
                </p>

                <Progress
                  value={uploadProgress}
                  className="max-w-sm mx-auto mt-4"
                  classNames={{
                    indicator: "bg-info h-1.5",
                    track: "h-1.5 bg-black/10 dark:bg-white/10",
                  }}
                />


              </div>
            )}

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500 mb-4">
                Or Connect with :
              </p>
              <div className="flex justify-center flex-wrap gap-2 lg:gap-4">

                {/* <Button
                  disabled={lightfLoading || youcanLoading || googleSheetLoading}
                  color="success"
                  startContent={
                    <img
                      alt="Continue with Shopify"
                      src={shopify}
                      className="h-6 w-6"
                    />

                  }
                  onClick={() => setshopifyInputOpen(true)}
                  className="min-w-[270px] rounded-full font-medium text-white bg-[#609C38] "
                >
                  Continue with Shopify
                </Button> */}
                {/*  YouCan button */}
                {/* <Button
                  disabled={lightfLoading || shopifyLoading || googleSheetLoading}
                  isLoading={youcanLoading}
                  onClick={() => handleYouCanConnect()}
                  variant="bordered"
                  startContent={
                    !youcanLoading && <img
                      alt="Continue with YouCan"
                      src={youcan}
                      className="h-8 w-8 " />
                  }
                  className="min-w-[270px] rounded-full  text-black dark:text-white font-bold border border-gray-400 "
                >
                  Continue with YouCan
                </Button> */}

                {/* Lightfunnels button */}
                {/* <Button
                  disabled={youcanLoading || shopifyLoading || googleSheetLoading}
                  isLoading={lightfLoading}
                  onClick={() => handleLightFConnect()}
                  variant="bordered"
                  startContent={
                    !lightfLoading && <img
                      alt="Continue with Lightfunnels"
                      src={lightfunnels}
                      className="h-5 w-5 rounded-full object-cover"
                    />

                  }
                  className="min-w-[270px] rounded-full  text-black dark:text-white font-bold border border-gray-400 "
                >
                  Continue with Lightfunnels
                </Button> */}

                <Button
                  disabled={youcanLoading || lightfLoading || shopifyLoading}
                  isLoading={googleSheetLoading}
                  onClick={() => handleGoogleSheetConnect()}
                  variant="bordered"
                  startContent={!googleSheetLoading && <img alt="Continue with Google" src={googlesheet} className="h-6 w-6" />}
                  className="min-w-[270px] rounded-full font-bold border border-gray-400 "
                >
                  Continue with Google Sheet
                </Button>


              </div>
            </div>
          </div>
        </div> : <div className="w-full max-w-5xl mx-auto p-4 h-full">
          {isUploading ?
            <div className="text-center p-8 my-6 flex flex-col gap-1 justify-center items-center h-full">
              <img src={uploadFileSVG} alt="Upload File" className="mx-auto h-12 w-12 text-glb_blue mb-6" />
              <p className="font-medium">
                Importing in progress : {fileUploadedIndex}/{selectedDataSheet.length}
              </p>

              <Progress
                value={uploadProgress}
                className="max-w-sm mx-auto mt-4"
                classNames={{
                  indicator: "bg-info h-1.5",
                  track: "h-1.5 bg-black/10 dark:bg-white/10",
                }}
              />
            </div>
            : <div className="text-center flex flex-col pt-3 flex-wrap justify-center items-center">
              <div className="flex justify-between items-center w-full flex-wrap">
                <div className="flex justify-start items-center gap-3">
                  <h2 className="text-base font-medium">Select Data Sheet    </h2>
                  <h2 className="text-base font-medium">: </h2>
                  <img alt="Continue with Google" src={googlesheet} className="h-5 w-5" />
                  <h2 className="text-sm">{selectedDataSheet.length} file selected</h2>
                </div>
                <div onClick={() => DeleteGoogleAccount()} className={`${deleteingLoaidng ? 'pointer-events-none' : ''} flex justify-center items-center gap-3 cursor-pointer text-glb_red bg-glb_red_opacity rounded-full py-2 px-4 text-sm`}>
                  {deleteingLoaidng ? <Spinner size="sm" color="danger" /> : <Cancel01Icon size={15} />}
                  Disconnect
                </div>
              </div>

              <div className="flex flex-wrap  mt-8 p-8 rounded-lg  bg-[#********] dark:bg-[#ffffff05] max-h-[600px] overflow-y-auto justify-start items-start gap-3 w-full ">
                {googleSheetLoading ? <Spinner /> : sheets && sheets.length > 0 ?
                  sheets.map((sheet) => {
                    return (
                      <Tooltip content={sheet.name} color="primary" key={sheet.id}>
                        <div onClick={() => setSelectedDataSheet((prev) => {
                          const exists = prev.some((s) => s.id === sheet.id);
                          if (exists) {
                            // Remove the item if it already exists
                            return prev.filter((s) => s.id !== sheet.id);
                          } else {
                            // Add the item if it doesn't exist
                            return [...prev, { id: sheet.id, name: sheet.name }];
                          }
                        })} className={` ${selectedDataSheet.some(s => s.id === sheet.id) ? 'bg-[#00FF6633] hover:bg-[#00FF6625] ' : 'bg-[#********] dark:bg-[#ffffff10] hover:bg-[#00000008] hover:dark:bg-[#ffffff08]'} rounded-lg   p-[6px]  cursor-pointer w-[160px] h-[130px] gap-2 flex flex-col justify-start items-start`} key={sheet.id}>
                          <div className="flex justify-start items-center gap-1 w-full">
                            {loadingSheets[sheet.id] ? <Spinner size='sm' /> : <img alt="Continue with Google" src={googlesheet} className="h-4 w-4" />}
                            <h6 className="text-xs text-center lg:text-start font-normal  truncate">{sheet.name}</h6>
                          </div>
                          <div className="flex flex-grow min-w-full bg-white rounded-md">
                          </div>
                        </div></Tooltip>
                    )
                  })
                  :
                  <h2 className="text-xl text-center lg:text-start font-semibold mb-2 ">
                    No sheet available in your account
                  </h2>}
              </div>
            </div>}
        </div>}
    </CustomModal>
  );
};

export default FileUploadModal;
