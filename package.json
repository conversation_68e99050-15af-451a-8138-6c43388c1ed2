{"name": "cod-power-group-seller-dashboard", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@auth-kit/react-router": "^3.1.3", "@heroui/accordion": "2.2.16", "@heroui/avatar": "2.2.15", "@heroui/button": "2.2.19", "@heroui/card": "2.2.18", "@heroui/checkbox": "2.3.18", "@heroui/chip": "2.2.15", "@heroui/code": "2.2.14", "@heroui/date-picker": "2.3.19", "@heroui/dropdown": "2.3.19", "@heroui/input": "2.4.19", "@heroui/modal": "2.2.16", "@heroui/navbar": "2.2.17", "@heroui/pagination": "2.2.17", "@heroui/progress": "2.2.15", "@heroui/react": "2.7.8", "@heroui/select": "2.4.19", "@heroui/spacer": "2.2.14", "@heroui/switch": "2.2.17", "@heroui/system": "2.4.15", "@heroui/table": "2.2.18", "@heroui/tabs": "2.2.16", "@heroui/theme": "2.4.15", "@heroui/user": "2.2.15", "@internationalized/date": "^3.7.0", "@react-aria/i18n": "^3.12.7", "@react-stately/data": "^3.11.7", "@reduxjs/toolkit": "^2.3.0", "axios": "^1.7.9", "chart.js": "^4.4.5", "chartjs-adapter-moment": "^1.0.1", "cod-power-group-seller-dashboard": "file:", "dompurify": "^3.2.5", "dotenv": "^16.4.7", "framer-motion": "^11.11.7", "hugeicons-react": "^0.3.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lodash.startswith": "^4.2.1", "moment": "^2.30.1", "prop-types": "^15.8.1", "qrcode.react": "^4.2.0", "quill": "^2.0.2", "react": "^18.3.1", "react-apexcharts": "^1.6.0", "react-auth-kit": "^3.1.3", "react-chartjs-2": "^5.2.0", "react-date-range": "^2.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.10", "react-gauge-component": "^1.2.61", "react-google-recaptcha": "^3.1.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "react-transition-group": "^4.4.5", "react-verification-input": "^4.1.2", "recharts": "^2.13.0", "sonner": "^2.0.3", "styled-components": "^6.1.13", "xlsx-js-style": "^1.2.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "vite": "^5.4.8"}}